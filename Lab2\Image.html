<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Display Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-image: url('C:/Users/<USER>/Desktop/htmlimage2.jpeg');
            background-repeat: no-repeat;
            background-attachment: fixed;
            background-size: cover;
            color: white;
        }

        h1 {
            text-align: center;
            font-size: 36px;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px black;
        }

        img {
            display: block;
            margin: 20px auto;
            max-width: 80%;
            height: auto;
            border: 4px solid white;
            border-radius: 12px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }

        p {
            font-size: 18px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 10px;
            text-align: center;
            max-width: 80%;
            margin: 10px auto;
        }

        .creative-box {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            background-color: rgba(255, 255, 255, 0.8);
            color: black;
            padding: 20px;
            border: 2px dashed black;
            border-radius: 10px;
            margin: 40px auto;
            width: fit-content;
            box-shadow: 0 0 15px rgba(0,0,0,0.3);
        }

        footer {
            background-color: #111;
            color: #fff;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
    </style>
</head>
<body>

    <h1>Image Showcase</h1>

    <img src="C:/Users/<USER>/Desktop/HTMl image.jpg" alt="Image 1 - Trulli House">
    

    <img src="C:/Users/<USER>/Desktop/htmlimage2.jpeg" alt="Image 2 - Another Image">
    

    <picture>
        <source media="(min-width: 650px)" srcset="C:/Users/<USER>/Desktop/HTMl image.jpg">
        <source media="(min-width: 465px)" srcset="C:/Users/<USER>/Desktop/htmlimage2.jpeg">
        <img src="C:/Users/<USER>/Desktop/HTMl image.jpg" alt="Responsive Image Example">
    </picture>

    <div class="creative-box">
        Created by Barun Kumar Pattnaik<br>
        Reg. No: 23BCE11391
    </div>

    <footer>
         2025 Barun Kumar Pattnaik | 23BCE11391
    </footer>

</body>
</html>
