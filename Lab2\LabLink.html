<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Example with Styled Footer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f8ff;
        }

        a:link, a:visited {
            background-color: #f44336;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            border-radius: 8px;
            margin: 5px;
        }

        a:hover, a:active {
            background-color: darkred;
        }

        h2 {
            color: #333;
        }

        p {
            font-size: 18px;
        }

        img {
            margin-top: 10px;
        }

        .creative-box {
            border: 2px dashed green;
            background-color: #e0ffe0;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 30px auto;
            width: fit-content;
            border-radius: 12px;
        }

        footer {
            background-color: #222;
            color: #fff;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
    </style>
</head>
<body>

    <h2>Useful Links</h2>

    <a href="https://www.w3schools.com/">Visit W3Schools.com!</a>
    <a href="https://www.w3.org/">W3C</a>
    <a href="https://www.google.com/">Google</a>
    <a href="C:\Users\<USER>\Desktop\HTMl image.jpg">HTML Images</a>
    <a href="/css/default.asp">CSS Tutorial</a>
    <a href="Stern:<EMAIL>">Send Email</a>

    <br><br>

    <a href="#C4">Jump to Chapter 4</a>
    <a href="html_demo.html#C4">Jump to Chapter 4 (External)</a>

    <br><br>

    <a href="default.asp">
        <img src="smiley.gif" alt="HTML tutorial" style="width:42px;height:42px;">
    </a>

    <br><br>

    <button onclick="document.location='default.asp'">HTML Tutorial</button>

    <h2 id="C4">Chapter 4</h2>
    <p>This is Chapter 4 content.</p>

    <div class="creative-box">
        Created by Barun Kumar Pattnaik<br>
        Reg. No: 23BCE11391
    </div>

    <footer>
        2025 Barun Kumar Pattnaik | 23BCE11391
    </footer>

</body>
</html>
