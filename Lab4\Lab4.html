<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>CSS Tutorial</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>

  <!-- Hero with background image -->
  <section class="hero">
    <h1>CSS Tutorial</h1>
    <p class="hero-desc"><PERSON><PERSON><PERSON>, the towering Darknight Hero of Mondstadt, stands vigilant over his city—his fiery wings a symbol of unwavering resolve.</p>
  </section>

  <!-- Sections -->
  <section id="home" class="card">
    <h2>Home</h2>
    <p>Demonstrates basic CSS linking: a lightblue background, centered white headings, and Verdana paragraphs.</p>
  </section>

  <section id="syntax" class="card">
    <h2>Syntax</h2>
    <p class="red-center">In CSS, you write selectors followed by braces: <code>p { color: red; text-align: center; }</code></p>
  </section>

  <section id="selectors" class="card">
    <h2>Selectors</h2>
    <p id="para1" class="red-center">ID selector example</p>
    <p class="center red">Class selector example</p>
    <p class="center large red">Multiple classes combine styles.</p>
    <p class="universal">Universal selector: all elements in blue.</p>
  </section>

  <section id="comments" class="card">
    <h2>Comments</h2>
    <p>CSS single-line comment: <code>/* This is a comment */</code></p>
    <p>CSS multi-line comment:
      <code>/*
        This is a
        multi-line comment
      */</code>
    </p>
    <p>HTML comment (not shown): <code><!-- This is an HTML comment --></code></p>
  </section>

  <section id="errors" class="card">
    <h2>Common Errors</h2>
    <ul>
      <li>Missing semicolon: <code>color: red</code> instead of <code>color: red;</code></li>
      <li>Typo in property name: <code>colr: blue;</code></li>
      <li>Invalid values: <code>width: -100px;</code></li>
      <li>Extra colon: <code>color:: blue;</code></li>
    </ul>
  </section>

  <section id="colors" class="card">
    <h2>Colours</h2>
    <div class="colour-box bg-tomato">Background-color: Tomato</div>
    <div class="colour-box text-seagreen">Color: MediumSeaGreen</div>
    <div class="colour-box border-hex">Border: #ff6347</div>
  </section>

  <!-- Footer -->
  <footer>
    <p class="credit"> Barun Kumar Pattnaik | 23BCE11391</p>
  </footer>

</body>
</html>