:root {
  --primary-bg: lightblue;
  --card-bg: rgba(255,255,255,0.8);
  --card-shadow: rgba(0,0,0,0.1);
  --hero-height: 300px;
}

body {
  background-color: var(--primary-bg);
  margin: 0;
  font-family: <PERSON>erdana, sans-serif;
  color: #333;
}

/* Hero */
.hero {
  background: url('./diluc.jpg') center/cover no-repeat;
  height: 80vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-shadow: 1px 1px 3px #000;
}
.hero-desc {
  max-width: 600px;
  text-align: center;
  font-style: italic;
  margin: 0 20px;
}

/* Card style sections */
.card {
  background: var(--card-bg);
  margin: 20px auto;
  max-width: 800px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px var(--card-shadow);
}
.card h2 {
  margin-top: 0;
  color: red;
  text-align: center;
}

/* Centered red text */
.red-center {
  color: red;
  text-align: center;
}
.center {
  text-align: center;
}
.large {
  font-size: 1.2em;
}
.universal {
  color: blue;
  text-align: center;
}

/* Comments & Errors: no extra CSS needed */

/* Colours examples */
.colour-box {
  padding: 10px;
  margin: 10px auto;
  width: fit-content;
  border-radius: 4px;
}
.bg-tomato {
  background-color: Tomato;
  color: white;
}
.text-seagreen {
  background: #def;
  color: MediumSeaGreen;
}
.border-hex {
  border: 2px solid #ff6347;
  background: #fdf;
}

/* Footer credit */
footer {
  text-align: center;
  padding: 20px 0;
  background: #eee;
}
.credit {
  color: #0d0d0d;
  font-size: 0.9rem;
}