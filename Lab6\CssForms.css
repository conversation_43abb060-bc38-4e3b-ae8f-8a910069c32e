body {
  font-family: Arial, sans-serif;
  background-color: #f2f2f2;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #333;
}

form {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 5px;
  max-width: 600px;
  margin: auto;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

input[type=text], select, textarea {
  width: 100%;
  padding: 12px 20px;
  margin: 8px 0;
  box-sizing: border-box;
  display: inline-block;
  border: 2px solid #ccc;
  border-radius: 4px;
  background-color: #f8f8f8;
}

input[type=text]:focus {
  background-color: lightblue;
  border: 3px solid #555;
}

/* Input with image/icon */
input.search-icon {
  background-image: url('searchicon.png');
  background-position: 10px 10px;
  background-repeat: no-repeat;
  padding-left: 40px;
}

/* Animated search */
input.animated-search {
  transition: width 0.4s ease-in-out;
}
input.animated-search:focus {
  width: 100%;
}

textarea {
  height: 150px;
  resize: none;
}

input[type=button], input[type=submit], input[type=reset] {
  background-color: #04AA6D;
  color: white;
  padding: 16px 32px;
  margin: 4px 2px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

input[type=button]:hover,
input[type=submit]:hover,
input[type=reset]:hover {
  background-color: #039f62;
}

.credit {
  text-align: center;
  margin-top: 40px;
  font-size: 14px;
  color: #777;
}
