table, th, td {
  border: 1px solid;
}

.basic-border {
  margin-bottom: 30px;
}

.full-width {
  width: 100%;
  margin-bottom: 30px;
}

.collapsed-border {
  border-collapse: collapse;
  margin-bottom: 30px;
}

.row-heights th {
  height: 70px;
}
.row-heights {
  width: 100%;
  margin-bottom: 30px;
}

.centered-text td {
  text-align: center;
  width: 50%;
  margin-bottom: 30px;
}

.left-header th {
  text-align: left;
}

.bottom-align td {
  height: 50px;
  vertical-align: bottom;
}

.padded-cells th, .padded-cells td {
  padding: 15px;
  text-align: left;
}

.hover-stripe th, .hover-stripe td {
  border-bottom: 1px solid #ddd;
  padding: 15px;
  text-align: left;
}
.hover-stripe tr:nth-child(even) {
  background-color: #f2f2f2;
}
.hover-stripe tr:hover {
  background-color: coral;
}

.responsive {
  width: 100%;
  border-collapse: collapse;
}

footer {
  margin-top: 60px;
  font-size: 14px;
  text-align: center;
  color: #555;
}
