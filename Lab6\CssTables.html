<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CSS Tables Tutorial - <PERSON><PERSON></title>
  <link rel="stylesheet" href="CssTables.css" />
</head>
<body>

  <h1>CSS Table</h1>

  <h2>1. Basic Table with Border</h2>
  <table class="basic-border">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td><PERSON></td>
    </tr>
    <tr>
      <td>Lois</td>
      <td><PERSON></td>
    </tr>
  </table>

  <h2>2. Full-Width Table</h2>
  <table class="full-width">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td><PERSON></td>
    </tr>
  </table>

  <h2>3. Collapsed Borders</h2>
  <table class="collapsed-border">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
    </tr>
  </table>

  <h2>4. Table with Row Heights</h2>
  <table class="row-heights">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
      <th>Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>5. Centered Cells</h2>
  <table class="centered-text">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
      <th>Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>6. Left-Aligned Header</h2>
  <table class="left-header">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
      <th>Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>7. Bottom Aligned Text</h2>
  <table class="bottom-align">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
      <th>Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>8. Padded Cells</h2>
  <table class="padded-cells">
    <tr>
      <th>Firstname</th>
      <th>Lastname</th>
      <th>Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>9. Table with Hover and Zebra Striping</h2>
  <table class="hover-stripe">
    <tr>
      <th style="background-color: #04AA6D; color: white;">First Name</th>
      <th style="background-color: #04AA6D; color: white;">Last Name</th>
      <th style="background-color: #04AA6D; color: white;">Savings</th>
    </tr>
    <tr>
      <td>Peter</td>
      <td>Griffin</td>
      <td>$100</td>
    </tr>
    <tr>
      <td>Lois</td>
      <td>Griffin</td>
      <td>$150</td>
    </tr>
    <tr>
      <td>Joe</td>
      <td>Swanson</td>
      <td>$300</td>
    </tr>
  </table>

  <h2>10. Responsive Table</h2>
  <div style="overflow-x:auto;">
    <table class="responsive">
      <tr>
        <th>First Name</th>
        <th>Last Name</th>
        <th colspan="12">Points (12 months)</th>
      </tr>
      <tr>
        <td>Jill</td>
        <td>Smith</td>
        <td colspan="12">50</td>
      </tr>
      <tr>
        <td>Eve</td>
        <td>Jackson</td>
        <td colspan="12">94</td>
      </tr>
      <tr>
        <td>Adam</td>
        <td>Johnson</td>
        <td colspan="12">67</td>
      </tr>
    </table>
  </div>

  <footer>
    <p>Barun Kumar Pattanaik | Reg. No: 23BCE11391</p>
  </footer>

</body>
</html>
