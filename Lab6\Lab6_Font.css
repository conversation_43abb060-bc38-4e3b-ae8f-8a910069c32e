/* ========= Global Styles ========= */
body {
    background-color: rgb(207, 10, 10);
    font-family: "Sofia", Verdana, sans-serif;
    font-size: 16px;
    color: rgb(47, 45, 45);
    padding: 10px;
    line-height: 1.2;
  }
  
  /* ========= Headings ========= */
  h1 {
    font-family: Georgia, serif;
    font-size: 60px;
    color: white;
  }
  
  h2 {
    font-size: 1.875em; /* 30px */
    margin-bottom: 15px;
  }
  
  /* ========= Font Families ========= */
  .p1 {
    font-family: "Times New Roman", Times, serif;
  }
  
  .p2 {
    font-family: Arial, Helvetica, sans-serif;
  }
  
  .p3 {
    font-family: "Lucida Console", "Courier New", monospace;
  }
  
  /* ========= Font Styles ========= */
  .normal-style {
    font-style: normal;
  }
  
  .italic {
    font-style: italic;
  }
  
  .oblique {
    font-style: oblique;
  }
  
  /* ========= Font Weights ========= */
  .normal-weight {
    font-weight: normal;
  }
  
  .thick {
    font-weight: bold;
  }
  
  /* ========= Font Variants ========= */
  .small {
    font-variant: small-caps;
  }
  
  /* ========= Font Size Overrides ========= */
  h1 {
    font-size: 2.5em; /* 40px */
  }
  
  h2 {
    font-size: 1.875em; /* 30px */
  }
  
  p {
    font-family: Tahoma, Verdana, sans-serif;
    font-size: 0.875em; /* 14px */
    margin-bottom: 10px;
  }
  
  /* ========= Font Shorthand Examples ========= */
  p.a {
    font: 20px Arial, sans-serif;
  }
  
  p.b {
    font: italic small-caps bold 12px/30px Georgia, serif;
  }
  
  p.footer {
    font: 20px/30px Verdana, sans-serif;

  }
  