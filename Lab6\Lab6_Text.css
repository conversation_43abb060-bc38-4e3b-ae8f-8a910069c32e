/* ========= Base Reset ========= */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f0f0;
    color: #222;
    line-height: 1.6;
  }
  
  /* ========= Headings ========= */
  h1, h2, h3 {
    margin-bottom: 15px;
    border-radius: 6px;
  }
  
  /* h1 styles */
  h1 {
    background-color: #000;
    color: white;
    text-align: center;
    letter-spacing: 5px;
    text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;
    text-decoration: underline red solid;
  }
  
  /* h2 styles */
  h2 {
    color: green;
    text-align: left;
    letter-spacing: -2px;
    text-decoration: underline double blue;
  }
  
  /* h3 styles */
  h3 {
    color: #222;
    text-align: right;
    text-decoration: underline red double 5px;
    text-decoration-style: dotted;
  }
  
  /* ========= Paragraphs ========= */
  p {
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
  }
  
  /* Text-align-last */
  p.a { text-align-last: right; }
  p.b { text-align-last: center; }
  p.c { text-align-last: justify; }
  
  /* Text-transform */
  p.uppercase    { text-transform: uppercase; }
  p.lowercase    { text-transform: lowercase; }
  p.capitalize   { text-transform: capitalize; }
  
  /* Word-spacing */
  p.one { word-spacing: 10px; }
  p.two { word-spacing: -2px; }
  
  /* Text-decoration styles */
  p.ex1 {
    text-decoration: underline dashed;
  }
  
  p.ex2 {
    text-decoration: underline wavy;
  }
  
  p.ex3 {
    text-decoration: underline wavy red;
  }
  
  /* ========= Div Styling ========= */
  div {
    background-color: #007BFF;
    color: white;
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-weight: bold;
    box-shadow: 2px 2px 10px rgba(0, 123, 255, 0.2);
  }
  
  /* ========= Footer ========= */
  p.footer {
    margin-top: 40px;
    text-align: center;
    font-weight: 600;
    font-size: 1.1em;
    color: #555;
    border-top: 1px solid #ccc;
    padding-top: 10px;
  }
  
  /* ========= Hover Effects ========= */
  p:hover, div:hover {
    background-color: #e0e0e0;
    transition: background-color 0.3s ease;
  }
  
  h1:hover, h2:hover, h3:hover {
    opacity: 0.85;
    transition: opacity 0.3s ease;
  }
  