/* ===== Base Reset ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ===== Body Styling ===== */
body {
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
  color: #222;
  line-height: 1.6;
}

/* ===== Header & Footer ===== */
header, footer {
  background-color: #1a1a1a;
  color: white;
  text-align: center;
  padding: 15px 0;
  font-family: 'Segoe UI', sans-serif;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

header h1 {
  font-size: 32px;
}

footer p {
  font-size: 16px;
}

/* ===== Text Overflow Handling ===== */
p.test1 {
  white-space: nowrap;
  width: 200px;
  border: 1px solid #000;
  overflow: hidden;
  text-overflow: clip;
  margin-bottom: 15px;
}

p.test2 {
  white-space: nowrap;
  width: 200px;
  border: 1px solid #000;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 15px;
}

/* ===== Div Hover Overflow ===== */
div.test:hover {
  overflow: visible;
  background-color: #f0f0f0;
}

/* ===== Word Wrapping and Breaking ===== */
p {
  word-wrap: break-word;
}

p.test1 {
  word-break: keep-all;
}

p.test2 {
  word-break: break-all;
}

/* ===== Writing Modes ===== */
p.test1 {
  writing-mode: horizontal-tb;
}

p.test2 {
  writing-mode: vertical-rl;
  height: 200px;
  border: 1px dashed #666;
  padding: 10px;
  margin-bottom: 20px;
}

span.test2 {
  display: inline-block;
  writing-mode: vertical-rl;
  border: 1px solid #000;
  padding: 10px;
  margin-top: 10px;
}
