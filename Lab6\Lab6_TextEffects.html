<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Text Overflow & Writing Mode</title>
  <link rel="stylesheet" href="Lab6_TextEffects.css">
</head>
<body>

  <h2><PERSON><PERSON> | Reg No: 23BCE11391</h2>

  <p class="test1">This is a long text that will not wrap and will be clipped at the end of the container.</p>

  <p class="test2">This is a long text that will not wrap and will end with an ellipsis if it overflows.</p>

  <h2>Div Hover to Show Overflow</h2>
  <div class="test" style="width: 200px; height: 40px; overflow: hidden; border: 1px solid black;">
    Hover to see more content: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin imperdiet.
  </div>

  <h2>Word Break and Wrapping</h2>
  <p class="test1">ThisIsAReallyLongUnbreakableWordThatShouldNotBreakEvenWhenItExceedsTheContainer</p>

  <p class="test2">ThisIsAReallyLongUnbreakableWordThatShouldBreakAnywhereWhenItExceedsTheContainer</p>

  <h2>Writing Mode</h2>
  <p class="test1">This is horizontal text (default).</p>

  <p class="test2">This is vertical text using <code>writing-mode: vertical-rl</code>.</p>

  <span class="test2">Vertical span</span>

  <h2>Barun Kumar Pattanaik | Reg No: 23BCE11391</h2>


</body>
</html>
